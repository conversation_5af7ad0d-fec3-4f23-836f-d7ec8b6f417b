# 环境配置说明

Firespoon API 支持三种运行环境：本地开发环境、测试环境和生产环境。每个环境都需要特定的配置文件。

## 环境文件

1. `.env.local` - 本地开发环境
   - 用于本地开发和测试
   - MongoDB 使用本地实例
   - Redis 使用本地实例
   - 所有外部服务使用测试凭据

2. `.env.render` - 测试环境（render.com/railway.com）
   - 用于远程测试和验证
   - 使用独立的测试数据库
   - 使用render 的 Redis 实例
   - 使用测试环境的外部服务凭据

3. `.env.prod` - 生产环境
   - 用于实际生产部署
   - 使用生产数据库
   - 使用生产 Redis 实例
   - 使用生产环境的外部服务凭据

## 配置要求

所有环境变量都必须显式设置，不允许使用默认值。缺少必需的环境变量将导致应用程序启动失败。

### 必需的环境变量

```
NODE_ENV                       - 运行环境 (development/test/production)
LOG_LEVEL                      - 默认日志级别
CONSOLE_LOG_LEVEL              - 控制台日志级别 (可选，默认使用 LOG_LEVEL)
FILE_LOG_LEVEL                 - 文件日志级别 (可选，默认使用 LOG_LEVEL)
SERVER_URL                     - 服务器 URL
PORT                          - 服务器端口
CONNECTION_STRING             - MongoDB 连接字符串
REDIS_URL                     - Redis 连接 URL
REDIS_ENABLE_TLS             - Redis TLS 启用状态
REDIS_PASSWORD               - Redis 密码
PAYEMOJI_CLIENT_ID           - Payemoji API 客户端 ID
PAYEMOJI_CLIENT_SECRET       - Payemoji API 客户端密钥
PAYEMOJI_WEBHOOK_SECRET      - Payemoji Webhook 密钥
```

## 日志级别配置

Firespoon API 支持为控制台输出和日志文件设置不同的日志级别：

1. `LOG_LEVEL` - 设置默认的日志级别
2. `CONSOLE_LOG_LEVEL` - 设置控制台输出的日志级别（如果未设置，则使用 LOG_LEVEL）
3. `FILE_LOG_LEVEL` - 设置日志文件的日志级别（如果未设置，则使用 LOG_LEVEL）

支持的日志级别（从低到高）：
- `error` - 仅记录错误
- `warn` - 记录警告和错误
- `info` - 记录信息、警告和错误
- `http` - 记录 HTTP 请求、信息、警告和错误
- `debug` - 记录所有内容，包括调试信息

推荐的日志级别配置：

| 环境 | LOG_LEVEL | CONSOLE_LOG_LEVEL | FILE_LOG_LEVEL |
|------|-----------|-------------------|----------------|
| 本地开发 | debug | debug | info |
| 测试环境 | info | info | warn |
| 生产环境 | warn | warn | error |

## 设置说明

1. 复制相应的模板文件：
   ```bash
   # 本地环境
   cp .env.local.template .env.local
   cp .env.local .env

   # 测试环境
   cp .env.test.template .env.test
   
   # 生产环境
   cp .env.prod.template .env.prod
   ```

2. 根据实际情况填写环境变量值

3. 确保敏感信息（如密码、API密钥等）不会被提交到代码仓库

## 安全注意事项

1. 永远不要将包含实际凭据的 `.env` 文件提交到代码仓库
2. 生产环境的凭据应该通过安全的方式管理和分发
3. 定期轮换敏感凭据
4. 使用不同的凭据用于不同的环境

## 环境差异

### 本地环境 (.env.local)
- 使用本地服务器
- DEBUG 级别的日志
- 本地 MongoDB 和 Redis
- 测试用的外部服务凭据

### 测试环境 (.env.test)
- 使用 render.com/railway.com 托管
- INFO 级别的日志
- 云托管的 MongoDB 和 Redis
- 测试环境的外部服务凭据

### 生产环境 (.env.prod)
- 使用生产服务器
- WARN 级别的日志
- 生产级别的 MongoDB 和 Redis
- 生产环境的外部服务凭据
